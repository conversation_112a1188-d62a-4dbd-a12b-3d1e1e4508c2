<template>
  <el-dialog
    v-model="dialogVisible"
    title="会议详情"
    width="900px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="meeting-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会议标题" :span="2">
          <span class="meeting-title">{{ meetingData.title }}</span>
        </el-descriptions-item>
        
        <el-descriptions-item label="开始时间">
          {{ formatDateTime(meetingData.startTime) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="结束时间">
          {{ formatDateTime(meetingData.endTime) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="会议地点">
          {{ meetingData.location || '未设置' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="会议状态">
          <el-tag :type="MEETING_STATUS_COLOR[meetingData.status]">
            {{ MEETING_STATUS_TEXT[meetingData.status] }}
          </el-tag>
        </el-descriptions-item>
        
        <el-descriptions-item label="创建者">
          {{ meetingData.creator?.nickName || meetingData.creator?.userName || '未知' }}
        </el-descriptions-item>
        
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(meetingData.createdAt) }}
        </el-descriptions-item>
        
        <el-descriptions-item label="会议描述" :span="2">
          <div class="description-content">
            {{ meetingData.description || '无描述' }}
          </div>
        </el-descriptions-item>
        
        <el-descriptions-item label="会议纪要" :span="2" v-if="meetingData.notes">
          <div class="notes-content">
            {{ meetingData.notes }}
          </div>
        </el-descriptions-item>
        
        <el-descriptions-item label="附件" :span="2" v-if="attachments.length > 0">
          <div class="attachments-content">
            <el-tag
              v-for="(attachment, index) in attachments"
              :key="index"
              class="attachment-tag"
            >
              <el-link :href="attachment" target="_blank" :underline="false">
                {{ attachment }}
              </el-link>
            </el-tag>
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 参与者列表 -->
      <div class="participants-section">
        <h3 class="section-title">
          参与者列表 ({{ participants.length }})
          <el-button
            v-if="canManageParticipants"
            type="primary"
            size="small"
            @click="showParticipantManager = true"
          >
            管理参与者
          </el-button>
        </h3>
        
        <el-table :data="participants" style="width: 100%">
          <el-table-column prop="user.userName" label="用户名" width="120" />
          <el-table-column prop="user.nickName" label="昵称" width="120" />
          <el-table-column prop="user.email" label="邮箱" width="200" />
          <el-table-column label="参与状态" width="120">
            <template #default="scope">
              <el-tag :type="PARTICIPANT_STATUS_COLOR[scope.row.status]">
                {{ PARTICIPANT_STATUS_TEXT[scope.row.status] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                v-if="canUpdateStatus(scope.row)"
                type="primary"
                size="small"
                @click="updateStatus(scope.row, 'accepted')"
              >
                接受
              </el-button>
              <el-button
                v-if="canUpdateStatus(scope.row)"
                type="danger"
                size="small"
                @click="updateStatus(scope.row, 'declined')"
              >
                拒绝
              </el-button>
              <el-button
                v-if="canMarkAttended(scope.row)"
                type="success"
                size="small"
                @click="updateStatus(scope.row, 'attended')"
              >
                已参加
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button
          v-if="canEdit"
          type="primary"
          @click="editMeeting"
        >
          编辑会议
        </el-button>
      </div>
    </template>

    <!-- 参与者管理弹窗 -->
    <ParticipantManager
      v-if="showParticipantManager"
      v-model="showParticipantManager"
      :meeting-id="meetingData.ID"
      :participants="participants"
      @refresh="() => getMeetingDetail(meetingData.ID)"
    />
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  getMeetingById,
  updateParticipantStatus,
  MEETING_STATUS_TEXT,
  MEETING_STATUS_COLOR,
  PARTICIPANT_STATUS_TEXT,
  PARTICIPANT_STATUS_COLOR
} from '@/api/meeting'
import { useUserStore } from '@/pinia/modules/user'
import ParticipantManager from './ParticipantManager.vue'

const emit = defineEmits(['refresh', 'edit'])

const userStore = useUserStore()

// 响应式数据
const dialogVisible = ref(false)
const loading = ref(false)
const showParticipantManager = ref(false)
const meetingData = reactive({})
const participants = ref([])

// 计算属性
const attachments = computed(() => {
  if (!meetingData.attachments) return []
  try {
    return typeof meetingData.attachments === 'string' 
      ? JSON.parse(meetingData.attachments) 
      : meetingData.attachments
  } catch (e) {
    return []
  }
})

const canEdit = computed(() => {
  return meetingData.creatorId === userStore.userInfo.ID
})

const canManageParticipants = computed(() => {
  return meetingData.creatorId === userStore.userInfo.ID
})

// 打开对话框
const openDialog = async (meetingId) => {
  dialogVisible.value = true
  await getMeetingDetail(meetingId)
}

// 获取会议详情
const getMeetingDetail = async (meetingId) => {
  loading.value = true
  try {
    const res = await getMeetingById(meetingId)
    if (res.code === 0) {
      Object.assign(meetingData, res.data)
      participants.value = res.data.participants || []
    }
  } catch (error) {
    console.error('获取会议详情失败:', error)
    ElMessage.error('获取会议详情失败')
  } finally {
    loading.value = false
  }
}

// 检查是否可以更新状态
const canUpdateStatus = (participant) => {
  // 只有被邀请的用户本人可以接受或拒绝
  return participant.userId === userStore.userInfo.ID && 
         participant.status === 'invited'
}

// 检查是否可以标记已参加
const canMarkAttended = (participant) => {
  // 会议创建者可以标记任何已接受的参与者为已参加
  // 或者参与者本人可以标记自己为已参加
  return (meetingData.creatorId === userStore.userInfo.ID || 
          participant.userId === userStore.userInfo.ID) &&
         participant.status === 'accepted'
}

// 更新参与者状态
const updateStatus = async (participant, status) => {
  try {
    const res = await updateParticipantStatus({
      meetingId: meetingData.ID,
      status: status
    })
    
    if (res.code === 0) {
      ElMessage.success('状态更新成功')
      // 更新本地数据
      const index = participants.value.findIndex(p => p.ID === participant.ID)
      if (index !== -1) {
        participants.value[index].status = status
      }
      emit('refresh')
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('更新状态失败')
  }
}

// 编辑会议
const editMeeting = () => {
  emit('edit', meetingData)
  handleClose()
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  Object.keys(meetingData).forEach(key => {
    delete meetingData[key]
  })
  participants.value = []
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 暴露方法
defineExpose({
  openDialog
})
</script>

<style scoped>
.meeting-detail {
  padding: 20px 0;
}

.meeting-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.description-content,
.notes-content {
  max-height: 120px;
  overflow-y: auto;
  line-height: 1.6;
  white-space: pre-wrap;
}

.attachments-content {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-tag {
  margin: 0;
}

.participants-section {
  margin-top: 30px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
