<template>
  <el-dialog
    v-model="dialogVisible"
    title="管理参与者"
    width="600px"
    :before-close="handleClose"
  >
    <div class="participant-manager">
      <!-- 添加参与者 -->
      <div class="add-participant-section">
        <h4>添加参与者</h4>
        <el-select
          v-model="selectedUsers"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="搜索并选择用户"
          :remote-method="searchUsers"
          :loading="userLoading"
          style="width: 100%"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.ID"
            :label="`${user.nickName || user.userName} (${user.userName})`"
            :value="user.ID"
            :disabled="isUserAlreadyParticipant(user.ID)"
          />
        </el-select>
        <el-button
          type="primary"
          :disabled="selectedUsers.length === 0"
          @click="addParticipants"
          style="margin-top: 10px"
        >
          添加参与者
        </el-button>
      </div>

      <!-- 当前参与者列表 -->
      <div class="current-participants-section">
        <h4>当前参与者 ({{ participants.length }})</h4>
        <el-table :data="participants" style="width: 100%">
          <el-table-column prop="user.userName" label="用户名" width="120" />
          <el-table-column prop="user.nickName" label="昵称" width="120" />
          <el-table-column label="参与状态" width="120">
            <template #default="scope">
              <el-tag :type="PARTICIPANT_STATUS_COLOR[scope.row.status]">
                {{ PARTICIPANT_STATUS_TEXT[scope.row.status] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button
                type="danger"
                size="small"
                @click="removeParticipant(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList } from '@/api/user'
import { updateMeeting } from '@/api/meeting'
import { PARTICIPANT_STATUS_TEXT, PARTICIPANT_STATUS_COLOR } from '@/api/meeting'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  meetingId: {
    type: Number,
    required: true
  },
  participants: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'refresh'])

// 响应式数据
const userLoading = ref(false)
const userOptions = ref([])
const selectedUsers = ref([])

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const participants = computed(() => props.participants)

// 检查用户是否已经是参与者
const isUserAlreadyParticipant = (userId) => {
  return participants.value.some(p => p.userId === userId)
}

// 搜索用户
const searchUsers = async (query) => {
  if (!query) return
  
  userLoading.value = true
  try {
    const res = await getUserList({
      page: 1,
      pageSize: 20,
      username: query
    })
    
    if (res.code === 0) {
      userOptions.value = res.data.list || []
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

// 添加参与者
const addParticipants = async () => {
  if (selectedUsers.value.length === 0) return
  
  try {
    // 获取当前所有参与者ID
    const currentParticipantIds = participants.value.map(p => p.userId)
    
    // 合并新的参与者ID
    const allParticipantIds = [...new Set([...currentParticipantIds, ...selectedUsers.value])]
    
    // 调用更新会议API
    const res = await updateMeeting({
      id: props.meetingId,
      participantIds: allParticipantIds
    })
    
    if (res.code === 0) {
      ElMessage.success('参与者添加成功')
      selectedUsers.value = []
      emit('refresh')
    }
  } catch (error) {
    console.error('添加参与者失败:', error)
    ElMessage.error('添加参与者失败')
  }
}

// 移除参与者
const removeParticipant = async (participant) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除参与者"${participant.user?.nickName || participant.user?.userName}"吗？`,
      '移除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 获取当前所有参与者ID，排除要移除的
    const remainingParticipantIds = participants.value
      .filter(p => p.ID !== participant.ID)
      .map(p => p.userId)
    
    // 调用更新会议API
    const res = await updateMeeting({
      id: props.meetingId,
      participantIds: remainingParticipantIds
    })
    
    if (res.code === 0) {
      ElMessage.success('参与者移除成功')
      emit('refresh')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除参与者失败:', error)
      ElMessage.error('移除参与者失败')
    }
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  selectedUsers.value = []
  userOptions.value = []
}

// 监听对话框打开，初始化数据
watch(dialogVisible, (newVal) => {
  if (newVal) {
    selectedUsers.value = []
    userOptions.value = []
  }
})
</script>

<style scoped>
.participant-manager {
  padding: 20px 0;
}

.add-participant-section,
.current-participants-section {
  margin-bottom: 30px;
}

.add-participant-section h4,
.current-participants-section h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.dialog-footer {
  text-align: right;
}
</style>
