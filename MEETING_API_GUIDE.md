# 会议管理系统 API 使用指南

## 概述

已成功为您的项目创建了完整的会议管理系统，包含以下功能：

- 会议的创建、更新、删除、查询
- 参与者管理
- 会议状态跟踪
- 用户会议列表
- 会议统计信息

## 数据库表结构

### 1. meetings 表
- `id`: 主键
- `title`: 会议标题
- `description`: 会议描述
- `start_time`: 开始时间
- `end_time`: 结束时间
- `location`: 会议地点
- `status`: 会议状态 (pending/ongoing/completed/cancelled)
- `creator_id`: 创建者ID
- `attachments`: 会议附件 (JSON格式)
- `notes`: 会议纪要
- `created_at`, `updated_at`, `deleted_at`: 时间戳

### 2. meeting_participants 表
- `id`: 主键
- `meeting_id`: 会议ID
- `user_id`: 用户ID
- `status`: 参与状态 (invited/accepted/declined/attended)
- `created_at`, `updated_at`, `deleted_at`: 时间戳

## API 接口

### 1. 创建会议
```
POST /meeting/createMeeting
```

请求体：
```json
{
  "title": "项目讨论会",
  "description": "讨论项目进展",
  "startTime": "2024-01-20T10:00:00Z",
  "endTime": "2024-01-20T11:00:00Z",
  "location": "会议室A",
  "participantIds": [1, 2, 3],
  "attachments": ["file1.pdf", "file2.doc"]
}
```

### 2. 更新会议
```
PUT /meeting/updateMeeting
```

请求体：
```json
{
  "id": 1,
  "title": "更新后的会议标题",
  "description": "更新后的描述",
  "startTime": "2024-01-20T10:00:00Z",
  "endTime": "2024-01-20T11:00:00Z",
  "location": "会议室B",
  "status": "ongoing",
  "participantIds": [1, 2, 3, 4],
  "attachments": ["file1.pdf"],
  "notes": "会议纪要内容"
}
```

### 3. 删除会议
```
DELETE /meeting/deleteMeeting
```

请求体：
```json
{
  "id": 1
}
```

### 4. 获取会议详情
```
GET /meeting/findMeeting?id=1
```

### 5. 获取会议列表
```
GET /meeting/getMeetingList?page=1&pageSize=10&title=项目&status=pending
```

查询参数：
- `page`: 页码
- `pageSize`: 每页大小
- `title`: 会议标题（模糊搜索）
- `status`: 会议状态
- `creatorId`: 创建者ID
- `location`: 会议地点（模糊搜索）
- `startDate`: 开始日期范围
- `endDate`: 结束日期范围

### 6. 更新参与者状态
```
PUT /meeting/updateParticipantStatus
```

请求体：
```json
{
  "meetingId": 1,
  "status": "accepted"
}
```

### 7. 获取用户会议列表
```
GET /meeting/getUserMeetings?page=1&pageSize=10&status=accepted
```

### 8. 获取会议统计信息
```
GET /meeting/getMeetingStatistics
```

## 会议状态说明

- `pending`: 待开始
- `ongoing`: 进行中
- `completed`: 已结束
- `cancelled`: 已取消

## 参与者状态说明

- `invited`: 已邀请
- `accepted`: 已接受
- `declined`: 已拒绝
- `attended`: 已参加

## 权限说明

- 只有会议创建者可以修改和删除会议
- 所有用户都可以查看会议列表和详情
- 参与者可以更新自己的参与状态

## 使用步骤

1. 启动服务器：`go run main.go`
2. 数据库表会自动创建
3. API权限会自动初始化
4. 使用上述API接口进行会议管理

## 注意事项

- 所有API都需要JWT认证
- 时间格式使用RFC3339标准
- 会议时间冲突检测已实现
- 支持事务操作确保数据一致性
