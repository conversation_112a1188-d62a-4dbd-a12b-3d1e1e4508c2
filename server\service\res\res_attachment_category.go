package res

import (
	"errors"
	"gorm.io/gorm"
	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/res"
)

type AttachmentCategoryService struct{}

// AddCategory 创建/更新的分类
func (a *AttachmentCategoryService) AddCategory(req *res.ResAttachmentCategory) (err error) {
	// 检查是否已存在相同名称的分类
	if (!errors.Is(global.DY_DB.Take(&res.ResAttachmentCategory{}, "name = ? and pid = ?", req.Name, req.Pid).Error, gorm.ErrRecordNotFound)) {
		return errors.New("分类名称已存在")
	}
	if req.ID > 0 {
		if err = global.DY_DB.Model(&res.ResAttachmentCategory{}).Where("id = ?", req.ID).Updates(&res.ResAttachmentCategory{
			Name: req.Name,
			Pid:  req.Pid,
		}).Error; err != nil {
			return err
		}
	} else {
		if err = global.DY_DB.Create(&res.ResAttachmentCategory{
			Name: req.Name,
			Pid:  req.Pid,
		}).Error; err != nil {
			return err
		}
	}
	return nil
}

// DeleteCategory 删除分类
func (a *AttachmentCategoryService) DeleteCategory(id *int) error {
	var childCount int64
	global.DY_DB.Model(&res.ResAttachmentCategory{}).Where("pid = ?", id).Count(&childCount)
	if childCount > 0 {
		return errors.New("请先删除子级")
	}
	return global.DY_DB.Where("id = ?", id).Unscoped().Delete(&res.ResAttachmentCategory{}).Error
}

// GetCategoryList 分类列表
func (a *AttachmentCategoryService) GetCategoryList() (resList []*res.ResAttachmentCategory, err error) {
	var fileLists []res.ResAttachmentCategory
	err = global.DY_DB.Model(&res.ResAttachmentCategory{}).Find(&fileLists).Error
	if err != nil {
		return resList, err
	}
	return a.getChildrenList(fileLists, 0), nil
}

// getChildrenList 子类
func (a *AttachmentCategoryService) getChildrenList(categories []res.ResAttachmentCategory, parentID uint) []*res.ResAttachmentCategory {
	var tree []*res.ResAttachmentCategory
	for _, category := range categories {
		if category.Pid == parentID {
			category.Children = a.getChildrenList(categories, category.ID)
			tree = append(tree, &category)
		}
	}
	return tree
}
