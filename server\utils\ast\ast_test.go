package ast

import (
	"go/ast"
	"go/parser"
	"go/printer"
	"go/token"
	"os"
	"path/filepath"
	"testing"
	"whlxyc.cn/server/global"
)

func TestAst(t *testing.T) {
	filename := filepath.Join(global.DY_CONFIG.AutoCode.Root, global.DY_CONFIG.AutoCode.Server, "plugin", "gva", "plugin.go")
	fileSet := token.NewFileSet()
	file, err := parser.ParseFile(fileSet, filename, nil, parser.ParseComments)
	if err != nil {
		t.Error(err)
		return
	}
	err = ast.Print(fileSet, file)
	if err != nil {
		t.Error(err)
		return
	}
	err = printer.Fprint(os.Stdout, token.NewFileSet(), file)
	if err != nil {
		panic(err)
	}

}
