package res

import (
	"errors"
	"whlxyc.cn/server/model/res"

	"gorm.io/gorm"
	"whlxyc.cn/server/global"
)

type FileUploadService struct{}

var FileUploadAndDownloadServiceApp = new(FileUploadService)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: FindOrCreateFile
//@description: 上传文件时检测当前文件属性，如果没有文件则创建，有则返回文件的当前切片
//@param: fileMd5 string, fileName string, chunkTotal int
//@return: file model.ExaFile, err error

func (e *FileUploadService) FindOrCreateFile(fileMd5 string, fileName string, chunkTotal int) (file res.ResFileBreakPoint, err error) {
	var cfile res.ResFileBreakPoint
	cfile.FileMd5 = fileMd5
	cfile.FileName = fileName
	cfile.ChunkTotal = chunkTotal

	if errors.Is(global.DY_DB.Where("file_md5 = ? AND is_finish = ?", fileMd5, true).First(&file).Error, gorm.ErrRecordNotFound) {
		err = global.DY_DB.Where("file_md5 = ? AND file_name = ?", fileMd5, fileName).Preload("ExaFileChunk").FirstOrCreate(&file, cfile).Error
		return file, err
	}
	cfile.IsFinish = true
	cfile.FilePath = file.FilePath
	err = global.DY_DB.Create(&cfile).Error
	return cfile, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateFileChunk
//@description: 创建文件切片记录
//@param: id uint, fileChunkPath string, fileChunkNumber int
//@return: error

func (e *FileUploadService) CreateFileChunk(id uint, fileChunkPath string, fileChunkNumber int) error {
	var chunk res.ResFileBreakPointChunk
	chunk.FileChunkPath = fileChunkPath
	chunk.ResFileBreakPointID = id
	chunk.FileChunkNumber = fileChunkNumber
	err := global.DY_DB.Create(&chunk).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteFileChunk
//@description: 删除文件切片记录
//@param: fileMd5 string, fileName string, filePath string
//@return: error

func (e *FileUploadService) DeleteFileChunk(fileMd5 string, filePath string) error {
	var chunks []res.ResFileBreakPointChunk
	var file res.ResFileBreakPoint
	err := global.DY_DB.Where("file_md5 = ?", fileMd5).First(&file).
		Updates(map[string]interface{}{
			"IsFinish":  true,
			"file_path": filePath,
		}).Error
	if err != nil {
		return err
	}
	err = global.DY_DB.Where("exa_file_id = ?", file.ID).Delete(&chunks).Unscoped().Error
	return err
}
