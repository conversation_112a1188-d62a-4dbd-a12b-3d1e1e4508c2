package meeting

import (
	"errors"
	"time"

	"whlxyc.cn/server/global"
	meetingModel "whlxyc.cn/server/model/meeting"
	"whlxyc.cn/server/model/meeting/request"
	"whlxyc.cn/server/model/meeting/response"
)

type MeetingService struct{}

// CreateMeeting 创建会议
func (m *MeetingService) CreateMeeting(req *request.CreateMeetingRequest, creatorID uint) (meeting *meetingModel.Meeting, err error) {
	// 验证时间合理性
	if req.EndTime.Before(req.StartTime) {
		return nil, errors.New("结束时间不能早于开始时间")
	}

	// 检查时间冲突
	hasConflict, err := m.CheckTimeConflict(req.StartTime, req.EndTime, 0)
	if err != nil {
		return nil, err
	}
	if hasConflict {
		return nil, errors.New("会议时间与其他会议冲突")
	}

	meeting = &meetingModel.Meeting{
		Title:                req.Title,
		Description:          req.Description,
		StartTime:            req.StartTime,
		EndTime:              req.EndTime,
		Location:             req.Location,
		Type:                 req.Type,
		ExpertFeeEarly:       req.ExpertFeeEarly,
		ExpertFeeNormal:      req.ExpertFeeNormal,
		StudentFeeEarly:      req.StudentFeeEarly,
		StudentFeeNormal:     req.StudentFeeNormal,
		CorporateFeeEarly:    req.CorporateFeeEarly,
		CorporateFeeNormal:   req.CorporateFeeNormal,
		EarlyDeadline:        req.EarlyDeadline,
		RegistrationDeadline: req.RegistrationDeadline,
		MaxParticipants:      req.MaxParticipants,
		Status:               meetingModel.MeetingStatusDraft,
		CreatorID:            creatorID,
	}

	// 开启事务
	tx := global.DY_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建会议
	if err = tx.Create(meeting).Error; err != nil {
		tx.Rollback()
		return nil, err
	}

	// 添加参与者
	if len(req.ParticipantIDs) > 0 {
		participants := make([]meetingModel.MeetingParticipant, 0, len(req.ParticipantIDs))
		for _, userID := range req.ParticipantIDs {
			participants = append(participants, meetingModel.MeetingParticipant{
				MeetingID: meeting.ID,
				UserID:    userID,
				Status:    meetingModel.ParticipantStatusInvited,
			})
		}
		if err = tx.Create(&participants).Error; err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		return nil, err
	}

	return meeting, nil
}

// UpdateMeeting 更新会议
func (m *MeetingService) UpdateMeeting(req *request.UpdateMeetingRequest, userID uint) (err error) {
	// 验证时间合理性
	if req.EndTime.Before(req.StartTime) {
		return errors.New("结束时间不能早于开始时间")
	}

	// 检查会议是否存在
	var meeting meetingModel.Meeting
	if err := global.DY_DB.First(&meeting, req.ID).Error; err != nil {
		return errors.New("会议不存在")
	}

	// 检查权限
	if meeting.CreatorID != userID {
		return errors.New("无权限修改此会议")
	}

	// 检查时间冲突（排除当前会议）
	hasConflict, err := m.CheckTimeConflict(req.StartTime, req.EndTime, req.ID)
	if err != nil {
		return err
	}
	if hasConflict {
		return errors.New("会议时间与其他会议冲突")
	}

	// 开启事务
	tx := global.DY_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新会议基本信息
	updateData := map[string]interface{}{
		"title":                 req.Title,
		"description":           req.Description,
		"start_time":            req.StartTime,
		"end_time":              req.EndTime,
		"location":              req.Location,
		"type":                  req.Type,
		"expert_fee_early":      req.ExpertFeeEarly,
		"expert_fee_normal":     req.ExpertFeeNormal,
		"student_fee_early":     req.StudentFeeEarly,
		"student_fee_normal":    req.StudentFeeNormal,
		"corporate_fee_early":   req.CorporateFeeEarly,
		"corporate_fee_normal":  req.CorporateFeeNormal,
		"early_deadline":        req.EarlyDeadline,
		"registration_deadline": req.RegistrationDeadline,
		"max_participants":      req.MaxParticipants,
	}

	// 如果提供了状态，则更新状态
	if req.Status != "" {
		updateData["status"] = req.Status
	}

	if err = tx.Model(&meeting).Updates(updateData).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 更新参与者
	if req.ParticipantIDs != nil {
		// 删除原有参与者
		if err = tx.Where("meeting_id = ?", req.ID).Delete(&meetingModel.MeetingParticipant{}).Error; err != nil {
			tx.Rollback()
			return err
		}

		// 添加新参与者
		if len(req.ParticipantIDs) > 0 {
			participants := make([]meetingModel.MeetingParticipant, 0, len(req.ParticipantIDs))
			for _, userID := range req.ParticipantIDs {
				participants = append(participants, meetingModel.MeetingParticipant{
					MeetingID: req.ID,
					UserID:    userID,
					Status:    meetingModel.ParticipantStatusInvited,
				})
			}
			if err = tx.Create(&participants).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}

// DeleteMeeting 删除会议
func (m *MeetingService) DeleteMeeting(id uint, userID uint) (err error) {
	// 检查会议是否存在
	var meeting meetingModel.Meeting
	if err := global.DY_DB.First(&meeting, id).Error; err != nil {
		return errors.New("会议不存在")
	}

	// 检查权限
	if meeting.CreatorID != userID {
		return errors.New("无权限删除此会议")
	}

	// 开启事务
	tx := global.DY_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 删除参与者
	if err = tx.Where("meeting_id = ?", id).Delete(&meetingModel.MeetingParticipant{}).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 删除会议
	if err = tx.Delete(&meeting).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	return tx.Commit().Error
}

// GetMeetingByID 根据ID获取会议详情
func (m *MeetingService) GetMeetingByID(id uint) (meeting *meetingModel.Meeting, err error) {
	err = global.DY_DB.Preload("Creator").Preload("Participants.User").First(&meeting, id).Error
	return meeting, err
}

// GetMeetingList 获取会议列表
func (m *MeetingService) GetMeetingList(req *request.MeetingSearch) (meetings *response.MeetingListResponse, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := global.DY_DB.Model(&meetingModel.Meeting{})

	// 构建查询条件
	if req.Title != "" {
		db = db.Where("title LIKE ?", "%"+req.Title+"%")
	}
	if req.Status != "" {
		db = db.Where("status = ?", req.Status)
	}
	if req.CreatorID != nil {
		db = db.Where("creator_id = ?", *req.CreatorID)
	}
	if req.Location != "" {
		db = db.Where("location LIKE ?", "%"+req.Location+"%")
	}
	if req.StartDate != nil {
		db = db.Where("start_time >= ?", *req.StartDate)
	}
	if req.EndDate != nil {
		db = db.Where("end_time <= ?", req.EndDate.Add(24*time.Hour))
	}

	// 获取总数
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取列表
	var meetingList []meetingModel.Meeting
	err = db.Preload("Creator").Preload("Participants").Limit(limit).Offset(offset).Order("created_at DESC").Find(&meetingList).Error
	if err != nil {
		return
	}

	// 转换为响应格式
	meetingResponses := make([]response.MeetingResponse, len(meetingList))
	for i, meeting := range meetingList {
		meetingResponses[i] = response.MeetingResponse{
			Meeting:          meeting,
			ParticipantCount: len(meeting.Participants),
		}
	}

	meetings = &response.MeetingListResponse{
		List:     meetingResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return meetings, nil
}

// UpdateParticipantStatus 更新参与者状态
func (m *MeetingService) UpdateParticipantStatus(req *request.UpdateParticipantStatusRequest, userID uint) (err error) {
	// 查找参与者记录
	var participant meetingModel.MeetingParticipant
	err = global.DY_DB.Where("meeting_id = ? AND user_id = ?", req.MeetingID, userID).First(&participant).Error
	if err != nil {
		return errors.New("参与者记录不存在")
	}

	// 更新状态
	participant.Status = req.Status
	return global.DY_DB.Save(&participant).Error
}

// GetUserMeetings 获取用户会议列表
func (m *MeetingService) GetUserMeetings(req *request.UserMeetingRequest, userID uint) (meetings *response.MeetingListResponse, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)

	// 构建查询
	db := global.DY_DB.Table("meetings").
		Joins("LEFT JOIN meeting_participants ON meetings.id = meeting_participants.meeting_id").
		Where("meetings.creator_id = ? OR meeting_participants.user_id = ?", userID, userID).
		Group("meetings.id")

	// 添加状态过滤
	if req.Status != "" {
		if req.Status == "my_created" {
			db = db.Where("meetings.creator_id = ?", userID)
		} else if req.Status == "my_participated" {
			db = db.Where("meeting_participants.user_id = ?", userID)
		} else {
			db = db.Where("meetings.status = ?", req.Status)
		}
	}

	// 获取总数
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取列表
	var meetingList []meetingModel.Meeting
	err = db.Preload("Creator").Preload("Participants").Limit(limit).Offset(offset).Order("meetings.created_at DESC").Find(&meetingList).Error
	if err != nil {
		return
	}

	// 转换为响应格式
	meetingResponses := make([]response.MeetingResponse, len(meetingList))
	for i, meeting := range meetingList {
		meetingResponses[i] = response.MeetingResponse{
			Meeting:          meeting,
			ParticipantCount: len(meeting.Participants),
		}
	}

	meetings = &response.MeetingListResponse{
		List:     meetingResponses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return meetings, nil
}

// GetMeetingStatistics 获取会议统计信息
func (m *MeetingService) GetMeetingStatistics(userID uint) (stats *response.MeetingStatisticsResponse, err error) {
	stats = &response.MeetingStatisticsResponse{}

	// 总会议数
	err = global.DY_DB.Model(&meetingModel.Meeting{}).Count(&stats.TotalMeetings).Error
	if err != nil {
		return nil, err
	}

	// 待开始会议数
	err = global.DY_DB.Model(&meetingModel.Meeting{}).Where("status = ? AND start_time > ?", meetingModel.MeetingStatusPublished, time.Now()).Count(&stats.PendingMeetings).Error
	if err != nil {
		return nil, err
	}

	// 进行中会议数
	now := time.Now()
	err = global.DY_DB.Model(&meetingModel.Meeting{}).Where("status = ? AND start_time <= ? AND end_time >= ?", meetingModel.MeetingStatusPublished, now, now).Count(&stats.OngoingMeetings).Error
	if err != nil {
		return nil, err
	}

	// 已完成会议数
	err = global.DY_DB.Model(&meetingModel.Meeting{}).Where("status = ?", meetingModel.MeetingStatusCompleted).Count(&stats.CompletedMeetings).Error
	if err != nil {
		return nil, err
	}

	// 已取消会议数
	err = global.DY_DB.Model(&meetingModel.Meeting{}).Where("status = ?", meetingModel.MeetingStatusCancelled).Count(&stats.CancelledMeetings).Error
	if err != nil {
		return nil, err
	}

	// 我的会议数
	err = global.DY_DB.Model(&meetingModel.Meeting{}).Where("creator_id = ?", userID).Count(&stats.MyMeetings).Error
	if err != nil {
		return nil, err
	}

	// 我参与的会议数
	err = global.DY_DB.Model(&meetingModel.MeetingParticipant{}).Where("user_id = ?", userID).Count(&stats.MyParticipations).Error
	if err != nil {
		return nil, err
	}

	return stats, nil
}

// CheckTimeConflict 检查时间冲突
func (m *MeetingService) CheckTimeConflict(startTime, endTime time.Time, excludeID uint) (bool, error) {
	var count int64
	db := global.DY_DB.Model(&meetingModel.Meeting{}).
		Where("(start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?) OR (start_time >= ? AND end_time <= ?)",
			endTime, startTime, startTime, endTime, startTime, endTime)

	if excludeID > 0 {
		db = db.Where("id != ?", excludeID)
	}

	err := db.Count(&count).Error
	return count > 0, err
}
