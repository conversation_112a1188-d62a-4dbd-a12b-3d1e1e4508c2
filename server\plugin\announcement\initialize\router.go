package initialize

import (
	"github.com/gin-gonic/gin"
	"whlxyc.cn/server/global"
	"whlxyc.cn/server/middleware"
	"whlxyc.cn/server/plugin/announcement/router"
)

func Router(engine *gin.Engine) {
	public := engine.Group(global.DY_CONFIG.System.RouterPrefix).Group("")
	private := engine.Group(global.DY_CONFIG.System.RouterPrefix).Group("")
	private.Use(middleware.JWTAuth()).Use(middleware.CasbinHandler())
	router.Router.Info.Init(public, private)
}
