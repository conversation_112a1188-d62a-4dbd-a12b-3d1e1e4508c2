package response

import "whlxyc.cn/server/model/meeting"

// MeetingResponse 会议响应结构体
type MeetingResponse struct {
	meeting.Meeting
	ParticipantCount int `json:"participantCount"` // 参与者数量
}

// MeetingListResponse 会议列表响应
type MeetingListResponse struct {
	List     []MeetingResponse `json:"list"`     // 会议列表
	Total    int64             `json:"total"`    // 总数
	Page     int               `json:"page"`     // 当前页
	PageSize int               `json:"pageSize"` // 每页大小
}

// MeetingDetailResponse 会议详情响应
type MeetingDetailResponse struct {
	meeting.Meeting
	CanEdit   bool `json:"canEdit"`   // 是否可编辑
	CanDelete bool `json:"canDelete"` // 是否可删除
}

// ParticipantResponse 参与者响应
type ParticipantResponse struct {
	meeting.MeetingParticipant
}

// MeetingCalendarResponse 会议日历响应
type MeetingCalendarResponse struct {
	Date     string            `json:"date"`     // 日期 YYYY-MM-DD
	Meetings []MeetingResponse `json:"meetings"` // 当天的会议列表
}

// MeetingStatisticsResponse 会议统计响应
type MeetingStatisticsResponse struct {
	TotalMeetings     int64 `json:"totalMeetings"`     // 总会议数
	PendingMeetings   int64 `json:"pendingMeetings"`   // 待开始会议数
	OngoingMeetings   int64 `json:"ongoingMeetings"`   // 进行中会议数
	CompletedMeetings int64 `json:"completedMeetings"` // 已完成会议数
	CancelledMeetings int64 `json:"cancelledMeetings"` // 已取消会议数
	MyMeetings        int64 `json:"myMeetings"`        // 我的会议数
	MyParticipations  int64 `json:"myParticipations"`  // 我参与的会议数
}

// UserMeetingResponse 用户会议响应
type UserMeetingResponse struct {
	Meeting           MeetingResponse `json:"meeting"`           // 会议信息
	ParticipantStatus string          `json:"participantStatus"` // 参与状态
	IsCreator         bool            `json:"isCreator"`         // 是否为创建者
}

// MeetingConflictResponse 会议冲突检查响应
type MeetingConflictResponse struct {
	HasConflict      bool              `json:"hasConflict"`      // 是否有冲突
	ConflictMeetings []MeetingResponse `json:"conflictMeetings"` // 冲突的会议列表
}
