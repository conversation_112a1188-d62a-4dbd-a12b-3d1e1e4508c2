package initialize

import (
	"os"

	"whlxyc.cn/server/model/res"

	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/system"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

func Gorm() *gorm.DB {
	switch global.DY_CONFIG.System.DbType {
	case "mysql":
		global.DY_ACTIVE_DBNAME = &global.DY_CONFIG.Mysql.Dbname
		return GormMysql()
	case "pgsql":
		global.DY_ACTIVE_DBNAME = &global.DY_CONFIG.Pgsql.Dbname
		return GormPgSql()
	case "oracle":
		global.DY_ACTIVE_DBNAME = &global.DY_CONFIG.Oracle.Dbname
		return GormOracle()
	case "mssql":
		global.DY_ACTIVE_DBNAME = &global.DY_CONFIG.Mssql.Dbname
		return GormMssql()
	case "sqlite":
		global.DY_ACTIVE_DBNAME = &global.DY_CONFIG.Sqlite.Dbname
		return GormSqlite()
	default:
		global.DY_ACTIVE_DBNAME = &global.DY_CONFIG.Mysql.Dbname
		return GormMysql()
	}
}

func RegisterTables() {
	db := global.DY_DB
	err := db.AutoMigrate(

		system.SysApi{},
		system.SysIgnoreApi{},
		system.SysUser{},
		system.SysBaseMenu{},
		system.JwtBlacklist{},
		system.SysAuthority{},
		system.SysDictionary{},
		system.SysOperationRecord{},
		system.SysAutoCodeHistory{},
		system.SysDictionaryDetail{},
		system.SysBaseMenuParameter{},
		system.SysBaseMenuBtn{},
		system.SysAuthorityBtn{},
		system.SysAutoCodePackage{},
		system.SysExportTemplate{},
		system.Condition{},
		system.JoinTemplate{},
		system.SysParams{},

		res.ResFileBreakPoint{},
		res.ExaCustomer{},
		res.ResFileBreakPointChunk{},
		res.ResFileUpload{},
		res.ResAttachmentCategory{},
	)
	if err != nil {
		global.DY_LOG.Error("register table failed", zap.Error(err))
		os.Exit(0)
	}

	err = bizModel()

	if err != nil {
		global.DY_LOG.Error("register biz_table failed", zap.Error(err))
		os.Exit(0)
	}
	global.DY_LOG.Info("register table success")
}
