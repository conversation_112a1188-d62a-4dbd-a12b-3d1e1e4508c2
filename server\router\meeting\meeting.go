package meeting

import (
	"github.com/gin-gonic/gin"
	v1 "whlxyc.cn/server/api/v1"
	"whlxyc.cn/server/middleware"
)

type MeetingRouter struct{}

// InitMeetingRouter 初始化会议路由
func (m *MeetingRouter) InitMeetingRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	meetingRouter := Router.Group("meeting").Use(middleware.OperationRecord())
	meetingRouterWithoutRecord := Router.Group("meeting")
	meetingPublicRouter := PublicRouter.Group("meeting")

	var meetingApi = v1.ApiGroupApp.MeetingApiGroup.MeetingApi

	{
		// 需要记录操作的路由
		meetingRouter.POST("createMeeting", meetingApi.CreateMeeting)                    // 创建会议
		meetingRouter.PUT("updateMeeting", meetingApi.UpdateMeeting)                     // 更新会议
		meetingRouter.DELETE("deleteMeeting", meetingApi.DeleteMeeting)                  // 删除会议
		meetingRouter.PUT("updateParticipantStatus", meetingApi.UpdateParticipantStatus) // 更新参与者状态
	}

	{
		// 不需要记录操作的路由
		meetingRouterWithoutRecord.GET("findMeeting", meetingApi.GetMeetingByID)                // 根据ID获取会议详情
		meetingRouterWithoutRecord.GET("getMeetingList", meetingApi.GetMeetingList)             // 获取会议列表
		meetingRouterWithoutRecord.GET("getUserMeetings", meetingApi.GetUserMeetings)           // 获取用户会议列表
		meetingRouterWithoutRecord.GET("getMeetingStatistics", meetingApi.GetMeetingStatistics) // 获取会议统计信息
	}

	{
		// 公开路由（如果需要的话，目前会议功能都需要登录）
		_ = meetingPublicRouter
	}
}
