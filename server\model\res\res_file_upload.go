package res

import (
	"whlxyc.cn/server/global"
)

type ResFileUpload struct {
	global.DY_MODEL
	Name    string `json:"name" form:"name" gorm:"column:name;comment:文件名"`                                // 文件名
	ClassId int    `json:"classId" form:"classId" gorm:"default:0;type:int;column:class_id;comment:分类id;"` // 分类id
	Url     string `json:"url" form:"url" gorm:"column:url;comment:文件地址"`                                  // 文件地址
	Tag     string `json:"tag" form:"tag" gorm:"column:tag;comment:文件标签"`                                  // 文件标签
	Key     string `json:"key" form:"key" gorm:"column:key;comment:编号"`                                    // 编号
}

func (ResFileUpload) TableName() string {
	return "res_file_upload"
}
