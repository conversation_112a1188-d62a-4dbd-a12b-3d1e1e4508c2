package request

import (
	"time"

	"whlxyc.cn/server/model/common/request"
)

// MeetingSearch 会议搜索结构体
type MeetingSearch struct {
	Title     string     `json:"title" form:"title"`         // 会议标题
	Status    string     `json:"status" form:"status"`       // 会议状态
	CreatorID *uint      `json:"creatorId" form:"creatorId"` // 创建者ID
	Location  string     `json:"location" form:"location"`   // 会议地点
	StartDate *time.Time `json:"startDate" form:"startDate"` // 开始日期范围
	EndDate   *time.Time `json:"endDate" form:"endDate"`     // 结束日期范围
	request.PageInfo
}

// CreateMeetingRequest 创建会议请求
type CreateMeetingRequest struct {
	Title                string    `json:"title" binding:"required"`                // 会议标题
	Description          string    `json:"description"`                             // 会议描述
	StartTime            time.Time `json:"startTime" binding:"required"`            // 开始时间
	EndTime              time.Time `json:"endTime" binding:"required"`              // 结束时间
	Location             string    `json:"location"`                                // 会议地点
	Type                 string    `json:"type"`                                    // 会议类型
	ExpertFeeEarly       float64   `json:"expertFeeEarly"`                          // 专家早鸟价
	ExpertFeeNormal      float64   `json:"expertFeeNormal"`                         // 专家正常价
	StudentFeeEarly      float64   `json:"studentFeeEarly"`                         // 学生早鸟价
	StudentFeeNormal     float64   `json:"studentFeeNormal"`                        // 学生正常价
	CorporateFeeEarly    float64   `json:"corporateFeeEarly"`                       // 企业代表早鸟价
	CorporateFeeNormal   float64   `json:"corporateFeeNormal"`                      // 企业代表正常价
	EarlyDeadline        time.Time `json:"earlyDeadline" binding:"required"`        // 早鸟截止时间
	RegistrationDeadline time.Time `json:"registrationDeadline" binding:"required"` // 注册截止时间
	MaxParticipants      *int      `json:"maxParticipants"`                         // 最大参与人数
	ParticipantIDs       []uint    `json:"participantIds"`                          // 参与者ID列表
}

// UpdateMeetingRequest 更新会议请求
type UpdateMeetingRequest struct {
	ID                   uint      `json:"id" binding:"required"`                   // 会议ID
	Title                string    `json:"title" binding:"required"`                // 会议标题
	Description          string    `json:"description"`                             // 会议描述
	StartTime            time.Time `json:"startTime" binding:"required"`            // 开始时间
	EndTime              time.Time `json:"endTime" binding:"required"`              // 结束时间
	Location             string    `json:"location"`                                // 会议地点
	Type                 string    `json:"type"`                                    // 会议类型
	ExpertFeeEarly       float64   `json:"expertFeeEarly"`                          // 专家早鸟价
	ExpertFeeNormal      float64   `json:"expertFeeNormal"`                         // 专家正常价
	StudentFeeEarly      float64   `json:"studentFeeEarly"`                         // 学生早鸟价
	StudentFeeNormal     float64   `json:"studentFeeNormal"`                        // 学生正常价
	CorporateFeeEarly    float64   `json:"corporateFeeEarly"`                       // 企业代表早鸟价
	CorporateFeeNormal   float64   `json:"corporateFeeNormal"`                      // 企业代表正常价
	EarlyDeadline        time.Time `json:"earlyDeadline" binding:"required"`        // 早鸟截止时间
	RegistrationDeadline time.Time `json:"registrationDeadline" binding:"required"` // 注册截止时间
	MaxParticipants      *int      `json:"maxParticipants"`                         // 最大参与人数
	Status               string    `json:"status"`                                  // 会议状态
	ParticipantIDs       []uint    `json:"participantIds"`                          // 参与者ID列表
}

// UpdateParticipantStatusRequest 更新参与者状态请求
type UpdateParticipantStatusRequest struct {
	MeetingID uint   `json:"meetingId" binding:"required"` // 会议ID
	Status    string `json:"status" binding:"required"`    // 参与状态
}

// GetMeetingByIDRequest 根据ID获取会议请求
type GetMeetingByIDRequest struct {
	ID uint `json:"id" form:"id" binding:"required"` // 会议ID
}

// DeleteMeetingRequest 删除会议请求
type DeleteMeetingRequest struct {
	ID uint `json:"id" binding:"required"` // 会议ID
}

// BatchDeleteMeetingRequest 批量删除会议请求
type BatchDeleteMeetingRequest struct {
	IDs []uint `json:"ids" binding:"required"` // 会议ID列表
}

// MeetingCalendarRequest 会议日历请求
type MeetingCalendarRequest struct {
	Year  int `json:"year" form:"year" binding:"required"`   // 年份
	Month int `json:"month" form:"month" binding:"required"` // 月份
}

// UserMeetingRequest 用户会议请求
type UserMeetingRequest struct {
	UserID *uint  `json:"userId" form:"userId"` // 用户ID，为空则查询当前用户
	Status string `json:"status" form:"status"` // 参与状态过滤
	request.PageInfo
}
