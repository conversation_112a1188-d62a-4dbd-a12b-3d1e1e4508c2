/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  important: true,
  theme: {
    extend: {
      backgroundColor: {
        main: '#F5F5F5'
      },
      textColor: {
        active: 'var(--el-color-primary)'
      },
      boxShadowColor: {
        active: 'var(--el-color-primary)'
      },
      borderColor: {
        'table-border': 'var(--el-border-color-lighter)'
      },
      backgroundImage: {
        'login-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'login-gradient-alt': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
        'card-gradient': 'linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
        'button-gradient': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        'button-gradient-hover': 'linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%)'
      },
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '8px',
        'lg': '12px',
        'xl': '16px'
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.6s ease-out',
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 2s infinite'
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(30px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' }
        }
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glass-inset': 'inset 0 1px 0 0 rgba(255, 255, 255, 0.05)',
        'button-hover': '0 10px 25px rgba(102, 126, 234, 0.4)',
        'card-hover': '0 20px 40px rgba(0, 0, 0, 0.1)'
      }
    }
  },
  darkMode: 'class',
  plugins: []
}
