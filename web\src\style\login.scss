/* 登录页面专用样式 */

// CSS变量定义
:root {
  --login-primary: #667eea;
  --login-secondary: #764ba2;
  --login-accent: #f093fb;
  --login-text: #2d3748;
  --login-text-light: #718096;
  --login-bg-glass: rgba(255, 255, 255, 0.1);
  --login-border-glass: rgba(255, 255, 255, 0.2);
  --login-shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

// 登录页面主容器
#userLayout {
  position: relative;
  overflow: hidden;
  background: #f8fafc;
}

// 旋转动画
@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 简洁登录卡片
.login-card {
  background: white;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  transition: all 0.3s ease;
  max-width: 560px;
  margin: 0 auto;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }
}



// 表单输入框增强
.el-input {
  .el-input__wrapper {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    transition: all 0.3s ease;
    padding: 12px 16px;

    &:hover {
      border-color: #3b82f6;
      background: #f1f5f9;
    }

    &.is-focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      background: white;
    }
  }
}

// 简洁按钮样式
.login-button {
  background: #3b82f6;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;

  &:hover {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// 验证码区域优化
.captcha-container {
  .captcha-image {
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

// Logo动画
.logo-container {
  img {
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    
    &:hover {
      transform: scale(1.1) rotate(5deg);
    }
  }
}

// 标题文字增强
.app-title {
  background: linear-gradient(135deg, var(--login-primary), var(--login-secondary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 800;
  letter-spacing: 1px;
}



// 底部链接优化
.bottom-links {
  a {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px) scale(1.1);
      filter: brightness(1.2);
    }
  }
}

// 加载状态
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-top: 3px solid var(--login-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 响应式优化
@media (max-width: 768px) {
  .login-card {
    margin: 1rem;
    padding: 1.5rem;
  }
  
  .oblique {
    display: none;
  }
}
