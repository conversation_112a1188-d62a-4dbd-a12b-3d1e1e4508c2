<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑会议' : '创建会议'"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="会议标题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入会议标题"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="formData.startTime"
              type="datetime"
              placeholder="选择开始时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="formData.endTime"
              type="datetime"
              placeholder="选择结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="会议地点" prop="location">
            <el-input
              v-model="formData.location"
              placeholder="请输入会议地点"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="isEdit">
        <el-col :span="24">
          <el-form-item label="会议状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option
                v-for="(text, value) in MEETING_STATUS_TEXT"
                :key="value"
                :label="text"
                :value="value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="参与者" prop="participantIds">
            <el-select
              v-model="formData.participantIds"
              multiple
              filterable
              remote
              reserve-keyword
              placeholder="请选择参与者"
              :remote-method="searchUsers"
              :loading="userLoading"
              style="width: 100%"
            >
              <el-option
                v-for="user in userOptions"
                :key="user.ID"
                :label="`${user.nickName || user.userName} (${user.userName})`"
                :value="user.ID"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="会议描述" prop="description">
            <el-input
              v-model="formData.description"
              type="textarea"
              :rows="4"
              placeholder="请输入会议描述"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="isEdit">
        <el-col :span="24">
          <el-form-item label="会议纪要" prop="notes">
            <el-input
              v-model="formData.notes"
              type="textarea"
              :rows="4"
              placeholder="请输入会议纪要"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="附件" prop="attachments">
            <el-input
              v-model="attachmentInput"
              placeholder="请输入附件URL，多个用逗号分隔"
              @blur="handleAttachmentInput"
            />
            <div v-if="formData.attachments && formData.attachments.length > 0" class="attachment-list">
              <el-tag
                v-for="(attachment, index) in formData.attachments"
                :key="index"
                closable
                @close="removeAttachment(index)"
                class="attachment-tag"
              >
                {{ attachment }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { createMeeting, updateMeeting, MEETING_STATUS_TEXT } from '@/api/meeting'
import { getUserList } from '@/api/user'

const emit = defineEmits(['refresh'])

// 响应式数据
const dialogVisible = ref(false)
const submitLoading = ref(false)
const userLoading = ref(false)
const isEdit = ref(false)
const userOptions = ref([])
const attachmentInput = ref('')

// 表单数据
const formData = reactive({
  id: null,
  title: '',
  description: '',
  startTime: '',
  endTime: '',
  location: '',
  status: 'pending',
  participantIds: [],
  attachments: [],
  notes: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入会议标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  location: [
    { max: 200, message: '地点长度不能超过 200 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 1000, message: '描述长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  notes: [
    { max: 2000, message: '纪要长度不能超过 2000 个字符', trigger: 'blur' }
  ]
}

// 自定义验证器
const validateTime = (rule, value, callback) => {
  if (formData.startTime && formData.endTime) {
    if (new Date(formData.endTime) <= new Date(formData.startTime)) {
      callback(new Error('结束时间必须晚于开始时间'))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 添加时间验证
rules.endTime.push({ validator: validateTime, trigger: 'change' })

// 打开对话框
const openDialog = (meeting = null) => {
  isEdit.value = !!meeting
  
  if (meeting) {
    // 编辑模式
    Object.keys(formData).forEach(key => {
      if (meeting[key] !== undefined) {
        formData[key] = meeting[key]
      }
    })
    
    // 处理附件数据
    if (meeting.attachments) {
      try {
        formData.attachments = typeof meeting.attachments === 'string' 
          ? JSON.parse(meeting.attachments) 
          : meeting.attachments
      } catch (e) {
        formData.attachments = []
      }
    }
    
    // 处理参与者数据
    if (meeting.participants && meeting.participants.length > 0) {
      formData.participantIds = meeting.participants.map(p => p.userId)
      // 将参与者添加到选项中
      userOptions.value = meeting.participants.map(p => p.user).filter(u => u)
    }
  } else {
    // 创建模式 - 重置表单
    resetForm()
  }
  
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 'pending'
    } else if (Array.isArray(formData[key])) {
      formData[key] = []
    } else {
      formData[key] = ''
    }
  })
  formData.id = null
  attachmentInput.value = ''
}

// 搜索用户
const searchUsers = async (query) => {
  if (!query) return
  
  userLoading.value = true
  try {
    const res = await getUserList({
      page: 1,
      pageSize: 20,
      username: query
    })
    
    if (res.code === 0) {
      userOptions.value = res.data.list || []
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    userLoading.value = false
  }
}

// 处理附件输入
const handleAttachmentInput = () => {
  if (attachmentInput.value.trim()) {
    const attachments = attachmentInput.value.split(',').map(item => item.trim()).filter(item => item)
    formData.attachments = [...new Set([...formData.attachments, ...attachments])]
    attachmentInput.value = ''
  }
}

// 移除附件
const removeAttachment = (index) => {
  formData.attachments.splice(index, 1)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const submitData = { ...formData }
    
    // 处理附件数据
    if (submitData.attachments && submitData.attachments.length > 0) {
      submitData.attachments = submitData.attachments
    } else {
      submitData.attachments = []
    }
    
    let res
    if (isEdit.value) {
      res = await updateMeeting(submitData)
    } else {
      delete submitData.id
      delete submitData.status
      delete submitData.notes
      res = await createMeeting(submitData)
    }
    
    if (res.code === 0) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      handleClose()
      emit('refresh')
    }
  } catch (error) {
    console.error('提交失败:', error)
    if (error !== 'validation failed') {
      ElMessage.error('提交失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  formRef.value?.resetFields()
  resetForm()
}

// 暴露方法
defineExpose({
  openDialog
})
</script>

<style scoped>
.attachment-list {
  margin-top: 8px;
}

.attachment-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
