package meeting

import (
	"time"

	"whlxyc.cn/server/global"
	"whlxyc.cn/server/model/system"
)

// Meeting 会议 结构体
type Meeting struct {
	global.DY_MODEL
	Title                string               `json:"title" form:"title" gorm:"column:title;comment:会议标题;not null" binding:"required"`                                                 // 会议标题
	Description          string               `json:"description" form:"description" gorm:"column:description;comment:会议描述;type:text"`                                                 // 会议描述
	StartTime            time.Time            `json:"startTime" form:"startTime" gorm:"column:start_time;comment:开始时间;not null" binding:"required"`                                    // 开始时间
	EndTime              time.Time            `json:"endTime" form:"endTime" gorm:"column:end_time;comment:结束时间;not null" binding:"required"`                                          // 结束时间
	Location             string               `json:"location" form:"location" gorm:"column:location;comment:会议地点"`                                                                    // 会议地点
	Type                 string               `json:"type" form:"type" gorm:"column:type;comment:会议类型;default:offline"`                                                                // 会议类型：online(线上)、offline(线下)、hybrid(混合)
	ExpertFeeEarly       float64              `json:"expertFeeEarly" form:"expertFeeEarly" gorm:"column:expert_fee_early;comment:专家早鸟价;default:2000.00"`                               // 专家早鸟价
	ExpertFeeNormal      float64              `json:"expertFeeNormal" form:"expertFeeNormal" gorm:"column:expert_fee_normal;comment:专家正常价;default:2200.00"`                            // 专家正常价
	StudentFeeEarly      float64              `json:"studentFeeEarly" form:"studentFeeEarly" gorm:"column:student_fee_early;comment:学生早鸟价;default:1500.00"`                            // 学生早鸟价
	StudentFeeNormal     float64              `json:"studentFeeNormal" form:"studentFeeNormal" gorm:"column:student_fee_normal;comment:学生正常价;default:1600.00"`                         // 学生正常价
	CorporateFeeEarly    float64              `json:"corporateFeeEarly" form:"corporateFeeEarly" gorm:"column:corporate_fee_early;comment:企业代表早鸟价;default:5000.00"`                    // 企业代表早鸟价
	CorporateFeeNormal   float64              `json:"corporateFeeNormal" form:"corporateFeeNormal" gorm:"column:corporate_fee_normal;comment:企业代表正常价;default:6000.00"`                 // 企业代表正常价
	EarlyDeadline        time.Time            `json:"earlyDeadline" form:"earlyDeadline" gorm:"column:early_deadline;comment:早鸟截止时间;not null" binding:"required"`                      // 早鸟截止时间
	RegistrationDeadline time.Time            `json:"registrationDeadline" form:"registrationDeadline" gorm:"column:registration_deadline;comment:注册截止时间;not null" binding:"required"` // 注册截止时间
	MaxParticipants      *int                 `json:"maxParticipants" form:"maxParticipants" gorm:"column:max_participants;comment:最大参与人数"`                                            // 最大参与人数
	Status               string               `json:"status" form:"status" gorm:"column:status;comment:会议状态;default:draft"`                                                            // 会议状态：draft(草稿)、published(已发布)、cancelled(已取消)、completed(已完成)
	CreatorID            uint                 `json:"creatorId" form:"creatorId" gorm:"column:creator_id;comment:创建者ID;not null"`                                                      // 创建者ID (虚拟字段，数据库中不存在但业务需要)
	Creator              system.SysUser       `json:"creator" gorm:"foreignKey:CreatorID"`                                                                                             // 创建者信息
	Participants         []MeetingParticipant `json:"participants" gorm:"foreignKey:MeetingID"`                                                                                        // 参与者列表
}

// TableName Meeting表名
func (Meeting) TableName() string {
	return "meetings"
}

// MeetingParticipant 会议参与者 结构体
type MeetingParticipant struct {
	global.DY_MODEL
	MeetingID uint           `json:"meetingId" form:"meetingId" gorm:"column:meeting_id;comment:会议ID;not null"` // 会议ID
	UserID    uint           `json:"userId" form:"userId" gorm:"column:user_id;comment:用户ID;not null"`          // 用户ID
	Status    string         `json:"status" form:"status" gorm:"column:status;comment:参与状态;default:invited"`    // 参与状态：invited(已邀请)、accepted(已接受)、declined(已拒绝)、attended(已参加)
	User      system.SysUser `json:"user" gorm:"foreignKey:UserID"`                                             // 用户信息
	Meeting   Meeting        `json:"meeting" gorm:"foreignKey:MeetingID"`                                       // 会议信息
}

// TableName MeetingParticipant表名
func (MeetingParticipant) TableName() string {
	return "meeting_participants"
}

// 会议状态常量
const (
	MeetingStatusDraft     = "draft"     // 草稿
	MeetingStatusPublished = "published" // 已发布
	MeetingStatusCancelled = "cancelled" // 已取消
	MeetingStatusCompleted = "completed" // 已完成
)

// 会议类型常量
const (
	MeetingTypeOnline  = "online"  // 线上
	MeetingTypeOffline = "offline" // 线下
	MeetingTypeHybrid  = "hybrid"  // 混合
)

// 参与者状态常量
const (
	ParticipantStatusInvited  = "invited"  // 已邀请
	ParticipantStatusAccepted = "accepted" // 已接受
	ParticipantStatusDeclined = "declined" // 已拒绝
	ParticipantStatusAttended = "attended" // 已参加
)
