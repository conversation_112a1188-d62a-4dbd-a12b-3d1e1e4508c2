package res

import (
	"whlxyc.cn/server/global"
)

// ResFileBreakPoint , 文件结构体
type ResFileBreakPoint struct {
	global.DY_MODEL
	FileName               string
	FileMd5                string
	FilePath               string
	ResFileBreakPointChunk []ResFileBreakPointChunk
	ChunkTotal             int
	IsFinish               bool
}

// ResFileBreakPointChunk , 切片结构体
type ResFileBreakPointChunk struct {
	global.DY_MODEL
	ResFileBreakPointID uint
	FileChunkNumber     int
	FileChunkPath       string
}
