package res

import (
	"whlxyc.cn/server/global"
)

type ResAttachmentCategory struct {
	global.DY_MODEL
	Name     string                   `json:"name" form:"name" gorm:"default:null;type:varchar(255);column:name;comment:分类名称;"`
	Pid      uint                     `json:"pid" form:"pid" gorm:"default:0;type:int;column:pid;comment:父节点ID;"`
	Children []*ResAttachmentCategory `json:"children" gorm:"-"`
}

func (ResAttachmentCategory) TableName() string {
	return "res_attachment_category"
}
