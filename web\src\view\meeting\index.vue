<template>
  <div class="meeting-container">
    <!-- 搜索区域 -->
    <div class="gva-search-box">
      <el-form ref="searchForm" :inline="true" :model="searchInfo">
        <el-form-item label="会议标题">
          <el-input v-model="searchInfo.title" placeholder="请输入会议标题" clearable />
        </el-form-item>
        <el-form-item label="会议状态">
          <el-select v-model="searchInfo.status" placeholder="请选择状态" clearable>
            <el-option
              v-for="(text, value) in MEETING_STATUS_TEXT"
              :key="value"
              :label="text"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会议地点">
          <el-input v-model="searchInfo.location" placeholder="请输入会议地点" clearable />
        </el-form-item>
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="searchInfo.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="searchInfo.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="onSubmit">查询</el-button>
          <el-button icon="Refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="Plus" @click="openMeetingForm()">
          创建会议
        </el-button>
        <el-button type="success" icon="TrendCharts" @click="showStatistics">
          统计信息
        </el-button>
      </div>

      <!-- 会议列表表格 -->
      <el-table
        :data="tableData"
        row-key="ID"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="ID" min-width="60" prop="ID" />
        <el-table-column align="left" label="会议标题" min-width="200" prop="title" />
        <el-table-column align="left" label="开始时间" min-width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="结束时间" min-width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="会议地点" min-width="150" prop="location" />
        <el-table-column align="left" label="状态" min-width="100">
          <template #default="scope">
            <el-tag :type="MEETING_STATUS_COLOR[scope.row.status]">
              {{ MEETING_STATUS_TEXT[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="创建者" min-width="120">
          <template #default="scope">
            {{ scope.row.creator?.nickName || scope.row.creator?.userName }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="参与人数" min-width="100">
          <template #default="scope">
            {{ scope.row.participantCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" min-width="240" fixed="right">
          <template #default="scope">
            <el-button type="primary" link icon="View" @click="viewMeeting(scope.row)">
              查看
            </el-button>
            <el-button
              v-if="canEdit(scope.row)"
              type="primary"
              link
              icon="Edit"
              @click="editMeeting(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="canDelete(scope.row)"
              type="danger"
              link
              icon="Delete"
              @click="deleteMeetingHandler(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="gva-pagination">
        <el-pagination
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 25, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 会议表单弹窗 -->
    <MeetingForm
      ref="meetingFormRef"
      @refresh="getTableData"
    />

    <!-- 会议详情弹窗 -->
    <MeetingDetail
      ref="meetingDetailRef"
      @refresh="getTableData"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getMeetingList,
  deleteMeeting,
  MEETING_STATUS_TEXT,
  MEETING_STATUS_COLOR
} from '@/api/meeting'
import { useUserStore } from '@/pinia/modules/user'
import MeetingForm from './components/MeetingForm.vue'
import MeetingDetail from './components/MeetingDetail.vue'

defineOptions({
  name: 'Meeting'
})

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([])

// 搜索表单
const searchInfo = reactive({
  title: '',
  status: '',
  location: '',
  startDate: '',
  endDate: ''
})

// 组件引用
const meetingFormRef = ref()
const meetingDetailRef = ref()

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      ...searchInfo
    }
    
    const res = await getMeetingList(params)
    if (res.code === 0) {
      tableData.value = res.data.list || []
      total.value = res.data.total || 0
    }
  } catch (error) {
    console.error('获取会议列表失败:', error)
    ElMessage.error('获取会议列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const onSubmit = () => {
  page.value = 1
  getTableData()
}

// 重置搜索
const onReset = () => {
  Object.keys(searchInfo).forEach(key => {
    searchInfo[key] = ''
  })
  page.value = 1
  getTableData()
}

// 分页处理
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  page.value = 1
  getTableData()
}

// 选择处理
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 打开会议表单
const openMeetingForm = (meeting = null) => {
  meetingFormRef.value.openDialog(meeting)
}

// 查看会议
const viewMeeting = (meeting) => {
  meetingDetailRef.value.openDialog(meeting.ID)
}

// 编辑会议
const editMeeting = (meeting) => {
  openMeetingForm(meeting)
}

// 删除会议
const deleteMeetingHandler = async (meeting) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会议"${meeting.title}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const res = await deleteMeeting({ id: meeting.ID })
    if (res.code === 0) {
      ElMessage.success('删除成功')
      getTableData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除会议失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 权限检查
const canEdit = (meeting) => {
  return meeting.creatorId === userStore.userInfo.ID
}

const canDelete = (meeting) => {
  return meeting.creatorId === userStore.userInfo.ID
}

// 显示统计信息
const showStatistics = () => {
  // TODO: 实现统计信息页面
  ElMessage.info('统计功能开发中...')
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.meeting-container {
  padding: 20px;
}

.gva-search-box {
  margin-bottom: 20px;
}

.gva-table-box {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.gva-btn-list {
  margin-bottom: 20px;
}

.gva-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
