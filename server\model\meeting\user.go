package meeting

import (
	"time"
	"whlxyc.cn/server/global"
)

type User struct {
	global.DY_MODEL
	Name              *string    `json:"name"`
	RealName          *string    `json:"realName" gorm:"column:real_name;comment:真实姓名"`                                 //真实姓名
	Gender            *string    `json:"gender" gorm:"column:gender;type:enum('male','female');comment:性别;"`              //性别
	Organization      *string    `json:"organization" gorm:"column:organization;type:varchar;size:255;comment:工作单位"`    //工作单位
	WillPresent       *bool      `json:"willPresent" gorm:"column:will_present;type:tinyint;size:1;comment:是否做会议报告"` //是否做会议报告
	JobTitle          *string    `json:"jobTitle" gorm:"column:job_title;type:varchar;size:191;comment:职称"`               //职称
	Email             *string    `json:"email" gorm:"column:email;type:varchar;size:191;"`
	Phone             *string    `json:"phone" gorm:"column:phone;type:varchar;size:191;comment:手机号"`                                  //手机号
	WechatOpenid      *string    `json:"wechatOpenid" gorm:"column:wechat_openid;type:varchar;size:191;comment:微信OpenID"`               //微信OpenID
	WechatUnionid     *string    `json:"wechatUnionid" gorm:"column:wechat_unionid;type:varchar;size:191;comment:微信UnionID"`            //微信UnionID
	Avatar            *string    `json:"avatar" gorm:"column:avatar;type:varchar;size:255;comment:头像"`                                  //头像
	MemberType        *string    `json:"memberType" gorm:"column:member_type;type:enum('expert','student','corporate');comment:会员类型"` //会员类型
	IsPaidUser        *bool      `json:"isPaidUser" gorm:"column:is_paid_user;type:tinyint;size:1;comment:是否为付费用户"`                //是否为付费用户
	PaidAt            *time.Time `json:"paidAt" gorm:"column:paid_at;type:datetime;comment:首次付费时间"`                                 //首次付费时间
	TotalPaidAmount   float64    `json:"totalPaidAmount" gorm:"column:total_paid_amount;type:float;comment:累计付费金额"`                 //累计付费金额
	PaidMeetingsCount int        `json:"paidMeetingsCount" gorm:"column:paid_meetings_count;type:int;size:10;comment:付费会议数量"`       //付费会议数量
	EmailVerifiedAt   *time.Time `json:"emailVerifiedAt" gorm:"column:email_verified_at;type:datetime;"`
	PhoneVerifiedAt   *time.Time `json:"phoneVerifiedAt" gorm:"column:phone_verified_at;type:datetime;"`
	Password          *string    `json:"password" gorm:"column:password;type:varchar;size:255;"`
	RememberToken     *string    `json:"rememberToken" gorm:"column:remember_token;type:varchar;size:255;"`
}

const (
	UserMemberTypeExpert    = "expert"
	UserMemberTypeStudent   = "student"
	UserMemberTypeCorporate = "corporate"
)
