<template>
  <div id="userLayout" class="w-full h-full relative bg-gray-50">
    <!-- 加载遮罩 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>

    <div
      class="rounded-lg flex items-center justify-center w-full h-full md:w-screen md:h-screen bg-gray-50"
    >
      <div class="w-full max-w-lg mx-auto px-6 animate-fade-in">
        <div
          class="login-card pt-16 pb-16 px-16 w-full rounded-lg flex flex-col justify-between box-border animate-slide-up"
        >
          <div>
            <div class="logo-container flex items-center justify-center mb-6">
              <img class="w-24 animate-float" :src="$GIN_VUE_ADMIN.appLogo" alt />
            </div>
            <div class="mb-12">
              <p class="app-title text-center text-4xl font-bold mb-4">
                {{ $GIN_VUE_ADMIN.appName }}
              </p>
              <p class="text-center text-sm font-normal text-gray-600 dark:text-gray-300 mt-3">
                A modern management platform using Golang and Vue
              </p>
            </div>
            <el-form
              ref="loginForm"
              :model="loginFormData"
              :rules="rules"
              :validate-on-rule-change="false"
              @keyup.enter="submitForm"
            >
              <el-form-item prop="username" class="mb-8">
                <el-input
                  v-model="loginFormData.username"
                 
                  placeholder="请输入用户名"
                  suffix-icon="user"
                  class="login-input"
                />
              </el-form-item>
              <el-form-item prop="password" class="mb-8">
                <el-input
                  v-model="loginFormData.password"
                  show-password
                  
                  type="password"
                  placeholder="请输入密码"
                  class="login-input"
                />
              </el-form-item>
              <el-form-item
                v-if="loginFormData.openCaptcha"
                prop="captcha"
                class="mb-8 captcha-container"
              >
                <div class="flex w-full justify-between items-center">
                  <el-input
                    v-model="loginFormData.captcha"
                    placeholder="请输入验证码"
                 
                    class="flex-1 mr-5 login-input"
                  />
                  <div class="w-1/3 h-11 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg overflow-hidden captcha-image">
                    <img
                      v-if="picPath"
                      class="w-full h-full object-cover cursor-pointer transition-transform duration-300 hover:scale-105"
                      :src="picPath"
                      alt="点击刷新验证码"
                      @click="loginVerify()"
                    />
                  </div>
                </div>
              </el-form-item>
              <el-form-item class="mb-8">
                <el-button
                  class="login-button h-12 w-full text-white font-semibold"
                  type="primary"
                  size="large"
                  :loading="isSubmitting"
                  @click="submitForm"
                  >{{ isSubmitting ? '登录中...' : '登 录' }}</el-button
                >
              </el-form-item>
              <el-form-item class="mb-0">
                <el-button
                  class="h-12 w-full border-2 border-gray-300 text-gray-600 hover:border-purple-400 hover:text-purple-600 transition-all duration-300"
                  size="large"
                  @click="checkInit"
                  >前往初始化</el-button
                >
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <BottomInfo class="left-0 right-0 absolute bottom-3 mx-auto w-full z-20">
      <div class="bottom-links links items-center justify-center gap-4 hidden md:flex">
        <a href="https://www.gin-vue-admin.com/" target="_blank" class="transition-all duration-300 hover:scale-110">
          <img src="@/assets/docs.png" class="w-8 h-8 rounded-lg shadow-md hover:shadow-lg" alt="文档" />
        </a>
        <a href="https://support.qq.com/product/371961" target="_blank" class="transition-all duration-300 hover:scale-110">
          <img src="@/assets/kefu.png" class="w-8 h-8 rounded-lg shadow-md hover:shadow-lg" alt="客服" />
        </a>
        <a
          href="https://github.com/flipped-aurora/gin-vue-admin"
          target="_blank"
          class="transition-all duration-300 hover:scale-110"
        >
          <img src="@/assets/github.png" class="w-8 h-8 rounded-lg shadow-md hover:shadow-lg" alt="github" />
        </a>
        <a href="https://space.bilibili.com/322210472" target="_blank" class="transition-all duration-300 hover:scale-110">
          <img src="@/assets/video.png" class="w-8 h-8 rounded-lg shadow-md hover:shadow-lg" alt="视频站" />
        </a>
      </div>
    </BottomInfo>
  </div>
</template>

<script setup>
  import { captcha } from '@/api/user'
  import { checkDB } from '@/api/initdb'
  import BottomInfo from '@/components/bottomInfo/bottomInfo.vue'
  import { reactive, ref } from 'vue'
  import { ElMessage } from 'element-plus'
  import { useRouter } from 'vue-router'
  import { useUserStore } from '@/pinia/modules/user'
  import '@/style/login.scss'

  defineOptions({
    name: 'Login'
  })

  const router = useRouter()
  // 验证函数
  const checkUsername = (rule, value, callback) => {
    if (value.length < 5) {
      return callback(new Error('请输入正确的用户名'))
    } else {
      callback()
    }
  }
  const checkPassword = (rule, value, callback) => {
    if (value.length < 6) {
      return callback(new Error('请输入正确的密码'))
    } else {
      callback()
    }
  }

  // 获取验证码
  const loginVerify = async () => {
    try {
      isLoading.value = true
      const ele = await captcha()
      rules.captcha.push({
        max: ele.data.captchaLength,
        min: ele.data.captchaLength,
        message: `请输入${ele.data.captchaLength}位验证码`,
        trigger: 'blur'
      })
      picPath.value = ele.data.picPath
      loginFormData.captchaId = ele.data.captchaId
      loginFormData.openCaptcha = ele.data.openCaptcha
    } catch (error) {
      console.error('获取验证码失败:', error)
      ElMessage.error('获取验证码失败，请重试')
    } finally {
      isLoading.value = false
    }
  }


  // 登录相关操作
  const loginForm = ref(null)
  const picPath = ref('')
  const isLoading = ref(false)
  const isSubmitting = ref(false)
  const loginFormData = reactive({
    username: 'admin',
    password: '',
    captcha: '',
    captchaId: '',
    openCaptcha: false
  })
  const rules = reactive({
    username: [{ validator: checkUsername, trigger: 'blur' }],
    password: [{ validator: checkPassword, trigger: 'blur' }],
    captcha: [
      {
        message: '验证码格式不正确',
        trigger: 'blur'
      }
    ]
  })

  const userStore = useUserStore()

  loginVerify()
  const login = async () => {
    return await userStore.LoginIn(loginFormData)
  }
  const submitForm = () => {
    loginForm.value.validate(async (v) => {
      if (!v) {
        // 未通过前端静态验证
        ElMessage({
          type: 'error',
          message: '请正确填写登录信息',
          showClose: true
        })
        await loginVerify()
        return false
      }

      try {
        isSubmitting.value = true
        // 通过验证，请求登陆
        const flag = await login()

        // 登陆失败，刷新验证码
        if (!flag) {
          await loginVerify()
          return false
        }

        // 登陆成功
        ElMessage({
          type: 'success',
          message: '登录成功！',
          showClose: true
        })
        return true
      } catch (error) {
        console.error('登录过程出错:', error)
        ElMessage({
          type: 'error',
          message: '登录过程出现错误，请重试',
          showClose: true
        })
        await loginVerify()
        return false
      } finally {
        isSubmitting.value = false
      }
    })
  }

  // 跳转初始化
  const checkInit = async () => {
    const res = await checkDB()
    if (res.code === 0) {
      if (res.data?.needInit) {
        userStore.NeedInit()
        await router.push({ name: 'Init' })
      } else {
        ElMessage({
          type: 'info',
          message: '已配置数据库信息，无法初始化'
        })
      }
    }
  }
</script>
