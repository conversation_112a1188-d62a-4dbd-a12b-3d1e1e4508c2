<template>
  <div class="statistics-container">
    <div class="page-header">
      <h2>会议统计</h2>
      <el-button @click="refreshData" :loading="loading">
        <el-icon><Refresh /></el-icon>
        刷新数据
      </el-button>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="statistics-cards">
      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <div class="stat-card total">
          <div class="stat-icon">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.totalMeetings }}</div>
            <div class="stat-label">总会议数</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <div class="stat-card pending">
          <div class="stat-icon">
            <el-icon><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.pendingMeetings }}</div>
            <div class="stat-label">待开始</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <div class="stat-card ongoing">
          <div class="stat-icon">
            <el-icon><VideoPlay /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.ongoingMeetings }}</div>
            <div class="stat-label">进行中</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8" :lg="6">
        <div class="stat-card completed">
          <div class="stat-icon">
            <el-icon><Check /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.completedMeetings }}</div>
            <div class="stat-label">已完成</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 个人统计 -->
    <el-row :gutter="20" class="personal-stats">
      <el-col :xs="24" :sm="12">
        <div class="stat-card my-meetings">
          <div class="stat-icon">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.myMeetings }}</div>
            <div class="stat-label">我创建的会议</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="24" :sm="12">
        <div class="stat-card my-participations">
          <div class="stat-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statistics.myParticipations }}</div>
            <div class="stat-label">我参与的会议</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>会议状态分布</span>
            </div>
          </template>
          <div ref="statusChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <el-col :xs="24" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>个人会议统计</span>
            </div>
          </template>
          <div ref="personalChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近会议 -->
    <el-card class="recent-meetings-card">
      <template #header>
        <div class="card-header">
          <span>最近会议</span>
          <el-button type="text" @click="$router.push('/meeting')">
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table :data="recentMeetings" v-loading="loadingRecent">
        <el-table-column prop="title" label="会议标题" min-width="200" />
        <el-table-column label="开始时间" min-width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" min-width="100">
          <template #default="scope">
            <el-tag :type="MEETING_STATUS_COLOR[scope.row.status]">
              {{ MEETING_STATUS_TEXT[scope.row.status] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="location" label="地点" min-width="150" />
        <el-table-column label="参与人数" min-width="100">
          <template #default="scope">
            {{ scope.row.participantCount || 0 }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'
import {
  getMeetingStatistics,
  getMeetingList,
  MEETING_STATUS_TEXT,
  MEETING_STATUS_COLOR
} from '@/api/meeting'

defineOptions({
  name: 'MeetingStatistics'
})

// 响应式数据
const loading = ref(false)
const loadingRecent = ref(false)
const statistics = reactive({
  totalMeetings: 0,
  pendingMeetings: 0,
  ongoingMeetings: 0,
  completedMeetings: 0,
  cancelledMeetings: 0,
  myMeetings: 0,
  myParticipations: 0
})
const recentMeetings = ref([])

// 图表引用
const statusChartRef = ref()
const personalChartRef = ref()
let statusChart = null
let personalChart = null

// 获取统计数据
const getStatistics = async () => {
  loading.value = true
  try {
    const res = await getMeetingStatistics()
    if (res.code === 0) {
      Object.assign(statistics, res.data)
      await nextTick()
      initCharts()
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 获取最近会议
const getRecentMeetings = async () => {
  loadingRecent.value = true
  try {
    const res = await getMeetingList({
      page: 1,
      pageSize: 5
    })
    if (res.code === 0) {
      recentMeetings.value = res.data.list || []
    }
  } catch (error) {
    console.error('获取最近会议失败:', error)
  } finally {
    loadingRecent.value = false
  }
}

// 初始化图表
const initCharts = () => {
  initStatusChart()
  initPersonalChart()
}

// 初始化状态分布图表
const initStatusChart = () => {
  if (statusChart) {
    statusChart.dispose()
  }
  
  statusChart = echarts.init(statusChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '会议状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: statistics.pendingMeetings, name: '待开始' },
          { value: statistics.ongoingMeetings, name: '进行中' },
          { value: statistics.completedMeetings, name: '已完成' },
          { value: statistics.cancelledMeetings, name: '已取消' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

// 初始化个人统计图表
const initPersonalChart = () => {
  if (personalChart) {
    personalChart.dispose()
  }
  
  personalChart = echarts.init(personalChartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['我创建的', '我参与的']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '会议数量',
        type: 'bar',
        data: [statistics.myMeetings, statistics.myParticipations],
        itemStyle: {
          color: function(params) {
            const colors = ['#5470c6', '#91cc75']
            return colors[params.dataIndex]
          }
        }
      }
    ]
  }
  
  personalChart.setOption(option)
}

// 刷新数据
const refreshData = async () => {
  await Promise.all([
    getStatistics(),
    getRecentMeetings()
  ])
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 页面加载时获取数据
onMounted(() => {
  refreshData()
})

// 页面卸载时销毁图表
onUnmounted(() => {
  if (statusChart) {
    statusChart.dispose()
  }
  if (personalChart) {
    personalChart.dispose()
  }
})
</script>

<style scoped>
.statistics-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.statistics-cards,
.personal-stats {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-card.total .stat-icon { background: #409eff; }
.stat-card.pending .stat-icon { background: #e6a23c; }
.stat-card.ongoing .stat-icon { background: #67c23a; }
.stat-card.completed .stat-icon { background: #909399; }
.stat-card.my-meetings .stat-icon { background: #f56c6c; }
.stat-card.my-participations .stat-icon { background: #9c88ff; }

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card,
.recent-meetings-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

@media (max-width: 768px) {
  .statistics-cards .el-col,
  .personal-stats .el-col {
    margin-bottom: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 12px;
  }
  
  .stat-number {
    font-size: 24px;
  }
}
</style>
