package global

import (
	"fmt"
	"github.com/mark3labs/mcp-go/server"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/qiniu/qmgo"

	"github.com/songzhibin97/gkit/cache/local_cache"
	"whlxyc.cn/server/utils/timer"

	"golang.org/x/sync/singleflight"

	"go.uber.org/zap"

	"whlxyc.cn/server/config"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

var (
	DY_DB        *gorm.DB
	DY_DBList    map[string]*gorm.DB
	DY_REDIS     redis.UniversalClient
	DY_REDISList map[string]redis.UniversalClient
	DY_MONGO     *qmgo.QmgoClient
	DY_CONFIG    config.Server
	DY_VP        *viper.Viper
	// DY_LOG    *oplogging.Logger
	DY_LOG                 *zap.Logger
	DY_Timer               timer.Timer = timer.NewTimerTask()
	DY_Concurrency_Control             = &singleflight.Group{}
	DY_ROUTERS             gin.RoutesInfo
	DY_ACTIVE_DBNAME       *string
	DY_MCP_SERVER          *server.MCPServer
	BlackCache             local_cache.Cache
	lock                   sync.RWMutex
)

// GetGlobalDBByDBName 通过名称获取db list中的db
func GetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	return DY_DBList[dbname]
}

// MustGetGlobalDBByDBName 通过名称获取db 如果不存在则panic
func MustGetGlobalDBByDBName(dbname string) *gorm.DB {
	lock.RLock()
	defer lock.RUnlock()
	db, ok := DY_DBList[dbname]
	if !ok || db == nil {
		panic("db no init")
	}
	return db
}

func GetRedis(name string) redis.UniversalClient {
	redis, ok := DY_REDISList[name]
	if !ok || redis == nil {
		panic(fmt.Sprintf("redis `%s` no init", name))
	}
	return redis
}
