package initialize

import (
	"github.com/mark3labs/mcp-go/server"
	"whlxyc.cn/server/global"
	mcpTool "whlxyc.cn/server/mcp"
)

func McpRun() *server.SSEServer {
	config := global.DY_CONFIG.MCP

	s := server.NewMCPServer(
		config.Name,
		config.Version,
	)

	global.DY_MCP_SERVER = s

	mcpTool.RegisterAllTools(s)

	return server.NewSSEServer(s,
		server.WithSSEEndpoint(config.SSEPath),
		server.WithMessageEndpoint(config.MessagePath),
		server.WithBaseURL(config.UrlPrefix))
}
